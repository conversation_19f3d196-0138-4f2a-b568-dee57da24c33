/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const BatteryWarning = createLucideIcon("BatteryWarning", [
  ["path", { d: "M10 17h.01", key: "nbq80n" }],
  ["path", { d: "M10 7v6", key: "nne03l" }],
  ["path", { d: "M14 7h2a2 2 0 0 1 2 2v6a2 2 0 0 1-2 2h-2", key: "1x5o8m" }],
  ["path", { d: "M22 11v2", key: "1wo06k" }],
  ["path", { d: "M6 7H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2", key: "1mdjgh" }]
]);

export { BatteryWarning as default };
//# sourceMappingURL=battery-warning.js.map
