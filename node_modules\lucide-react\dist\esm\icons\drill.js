/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const Drill = createLucideIcon("Drill", [
  [
    "path",
    { d: "M14 9c0 .6-.4 1-1 1H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9c.6 0 1 .4 1 1Z", key: "b6nnkj" }
  ],
  ["path", { d: "M18 6h4", key: "66u95g" }],
  ["path", { d: "M14 4h3a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-3", key: "105ega" }],
  ["path", { d: "m5 10-2 8", key: "xt2lic" }],
  ["path", { d: "M12 10v3c0 .6-.4 1-1 1H8", key: "mwpjnk" }],
  ["path", { d: "m7 18 2-8", key: "1bzku2" }],
  [
    "path",
    { d: "M5 22c-1.7 0-3-1.3-3-3 0-.6.4-1 1-1h7c.6 0 1 .4 1 1v2c0 .6-.4 1-1 1Z", key: "117add" }
  ]
]);

export { Drill as default };
//# sourceMappingURL=drill.js.map
