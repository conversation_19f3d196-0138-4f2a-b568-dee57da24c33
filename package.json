{"name": "budget-request-react", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"clsx": "^2.1.0", "lucide-react": "^0.453.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.52.0", "react-router-dom": "^6.26.1", "recharts": "^2.12.7", "zod": "^3.23.8", "zustand": "^4.5.4"}, "devDependencies": {"@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.20", "postcss": "^8.4.41", "tailwindcss": "^3.4.10", "typescript": "^5.5.4", "vite": "^5.4.2"}}